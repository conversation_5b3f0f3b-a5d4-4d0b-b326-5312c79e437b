#!/usr/bin/env python3
"""
Test script to verify polyline functionality works correctly.
"""

import sys
import os
sys.path.append('.')

from utils.general_utils import parse_polyline_string

def test_polyline_parsing():
    """Test the polyline parsing function."""
    
    # Test case 1: Comma-separated coordinates
    test_content_1 = """100.0,200.0
150.0,250.0
200.0,300.0
250.0,350.0
300.0,400.0"""
    
    print("Testing polyline parsing...")
    try:
        vertices = parse_polyline_string(test_content_1)
        print(f"✓ Successfully parsed {len(vertices)} vertices:")
        for i, (x, y) in enumerate(vertices):
            print(f"  Vertex {i+1}: ({x}, {y})")
        
        # Verify the results
        expected = [(100.0, 200.0), (150.0, 250.0), (200.0, 300.0), (250.0, 350.0), (300.0, 400.0)]
        if vertices == expected:
            print("✓ Parsing results match expected values")
        else:
            print("✗ Parsing results don't match expected values")
            print(f"Expected: {expected}")
            print(f"Got: {vertices}")
            
    except Exception as e:
        print(f"✗ Error parsing polyline: {e}")
        return False
    
    # Test case 2: Semicolon-separated format
    test_content_2 = "100.0,200.0; 150.0,250.0; 200.0,300.0"
    
    try:
        vertices_2 = parse_polyline_string(test_content_2)
        print(f"✓ Successfully parsed semicolon format: {len(vertices_2)} vertices")
        expected_2 = [(100.0, 200.0), (150.0, 250.0), (200.0, 300.0)]
        if vertices_2 == expected_2:
            print("✓ Semicolon format parsing correct")
        else:
            print("✗ Semicolon format parsing incorrect")
            
    except Exception as e:
        print(f"✗ Error parsing semicolon format: {e}")
        return False
    
    print("✓ All polyline parsing tests passed!")
    return True

if __name__ == "__main__":
    test_polyline_parsing()
